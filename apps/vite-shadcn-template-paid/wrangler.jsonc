{
  "$schema": "../../node_modules/wrangler/config-schema.json",
  "name": "vite-shadcn-template-paid",
  "main": "./workers/app.ts",
  "compatibility_date": "2025-08-06",
  "compatibility_flags": [
    "nodejs_compat",
    "global_fetch_strictly_public"
  ],
  "placement": { "mode": "smart" },
  "vars": {
    "VALUE_FROM_CLOUDFLARE": "Hello from Libra",
    "BETTER_AUTH_SECRET": "09b0de499a9ef0f51b3e3cacb797378dcde9c1a3bde668d14a961c913bd7ef3c"
  },
  // "observability": {
  //   "enabled": true
  // }
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "libra-local-paid",
      "database_id": "ce3afb9d-77a9-4a1a-8b8c-be2677ef37fd"
    }
  ]
}
