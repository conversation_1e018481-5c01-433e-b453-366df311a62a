import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";

// Create auth configuration function to avoid type inference issues
function createAuthConfig() {
  return betterAuth({
    // Database configuration using Drizzle adapter
    database: drizzleAdapter(
      // This will be replaced with actual D1 database in runtime
      {} as any,
      {
        provider: "sqlite",
        usePlural: true,
      }
    ),

    // Basic session configuration
    session: {
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
      cookieCache: {
        enabled: true,
        maxAge: 60 * 5, // 5 minutes
      },
    },

    // Email and password authentication
    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false,
    },

    // Social providers (optional - configure as needed)
    socialProviders: {
      github: {
        clientId: process.env.GITHUB_CLIENT_ID || "",
        clientSecret: process.env.GITHUB_CLIENT_SECRET || "",
      },
    },

    // Security settings
    secret: process.env.BETTER_AUTH_SECRET || "your-secret-key-here",
    baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",

    // Advanced configuration
    advanced: {
      generateId: () => crypto.randomUUID(),
    },
  });
}

// Basic better-auth configuration without Cloudflare wrapper
// This is a minimal setup for the template
export const auth = createAuthConfig() as any;

// Runtime auth configuration function for Hono applications
// Use this in your Hono routes to get auth instance with actual database
export function createAuth(database: any): any {
  return betterAuth({
    // Database configuration with actual D1 database
    database: drizzleAdapter(database, {
      provider: "sqlite",
      usePlural: true,
    }),

    // Basic session configuration
    session: {
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
      cookieCache: {
        enabled: true,
        maxAge: 60 * 5, // 5 minutes
      },
    },

    // Email and password authentication
    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false,
    },

    // Social providers (optional - configure as needed)
    socialProviders: {
      github: {
        clientId: process.env.GITHUB_CLIENT_ID || "",
        clientSecret: process.env.GITHUB_CLIENT_SECRET || "",
      },
    },

    // Security settings
    secret: process.env.BETTER_AUTH_SECRET || "your-secret-key-here",
    baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",

    // Advanced configuration
    advanced: {
      generateId: () => crypto.randomUUID(),
    },
  });
}

/*
Usage example in Hono application:

import { Hono } from "hono";
import { drizzle } from "drizzle-orm/d1";
import { createAuth } from "./auth";

const app = new Hono<{ Bindings: { DATABASE: D1Database } }>();

app.use("/api/auth/*", async (c, next) => {
  const db = drizzle(c.env.DATABASE);
  const authInstance = createAuth(db);

  // Handle auth requests
  return authInstance.handler(c.req.raw);
});

export default app;
*/