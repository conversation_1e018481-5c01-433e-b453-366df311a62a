{
  "$schema": "../../node_modules/wrangler/config-schema.json",
  "name": "vite-shadcn-template-paid",
  "main": "./workers/app.ts",
  "compatibility_date": "2025-08-06",
  "compatibility_flags": [
    "nodejs_compat",
    "global_fetch_strictly_public"
  ],
  "placement": { "mode": "smart" },
  "vars": {
    "VALUE_FROM_CLOUDFLARE": "Hello from Libra"
  }
  // "observability": {
  //   "enabled": true
  // }
}
