{"name": "@libra/docs", "version": "1.0.1", "private": true, "scripts": {"build": "next build", "dev": "bun next dev --turbo -p 3003", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "update": "bun update"}, "dependencies": {"fumadocs-core": "^15.6.9", "fumadocs-mdx": "^11.7.3", "fumadocs-ui": "^15.6.9", "motion": "^12.23.12", "octokit": "^5.0.3", "@orama/tokenizers": "^3.1.11", "@orama/orama": "^3.1.11", "zod": "^4.0.15"}, "devDependencies": {"@types/mdx": "^2.0.13"}}