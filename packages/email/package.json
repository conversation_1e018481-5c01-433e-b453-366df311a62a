{"name": "@libra/email", "version": "1.0.1", "private": true, "description": "Email templates and utilities for Libra", "main": "index.ts", "types": "./index.ts", "type": "module", "scripts": {"build": "tsup", "clean": "rm -rf dist .turbo node_modules", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --", "update": "bun update"}, "dependencies": {"@react-email/components": "^0.3.3", "@react-email/tailwind": "^1.2.2", "@t3-oss/env-nextjs": "^0.11.1", "resend": "^4.8.0", "zod": "^4.0.15"}, "devDependencies": {"@libra/typescript-config": "*"}}